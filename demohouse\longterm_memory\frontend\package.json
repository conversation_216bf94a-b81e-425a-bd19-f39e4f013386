{"name": "longtermmemory-fe", "version": "0.1.0", "scripts": {"reset": "npx rimraf node_modules ./**/node_modules", "dev": "modern dev", "build": "modern build", "start": "modern start", "serve": "modern serve", "new": "modern new", "lint": "biome check", "prepare": "simple-git-hooks", "upgrade": "modern upgrade"}, "engines": {"node": ">=16.18.1"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --files-ignore-unknown=true"]}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "dependencies": {"@arco-design/web-react": "^2.65.0", "@modern-js/plugin-tailwindcss": "2.65.3", "@modern-js/runtime": "2.65.3", "@types/js-cookie": "^3.0.6", "ahooks": "^3.8.4", "classnames": "^2.5.1", "dayjs": "^1.11.13", "eventsource-parser": "^3.0.0", "js-cookie": "^3.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@modern-js/app-tools": "2.65.3", "@modern-js/tsconfig": "2.65.3", "@types/jest": "~29.2.4", "@types/node": "~18.11.9", "@types/react": "^18.3.11", "@types/react-dom": "~18.3.1", "lint-staged": "~15.4.0", "rimraf": "^6.0.1", "simple-git-hooks": "^2.11.1", "tailwindcss": "~3.4.14", "typescript": "~5.7.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}