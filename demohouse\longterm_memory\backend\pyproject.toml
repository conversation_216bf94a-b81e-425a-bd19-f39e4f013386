[tool.poetry]
name = "longterm-memory"
version = "0.1.0"
description = "Set up the mem0 project based on the Apache 2.0 License"
authors = ["<EMAIL>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.9,<3.12.0"
arkitect = ">=0.0.1,<0.2.0"
mem0ai = { path = "mem0ai-********-py3-none-any.whl" }
volcengine = "1.0.164"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
