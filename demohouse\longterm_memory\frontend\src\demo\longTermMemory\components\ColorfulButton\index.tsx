// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// Licensed under the 【火山方舟】原型应用软件自用许可协议
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at 
//     https://www.volcengine.com/docs/82379/1433703
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { CSSProperties, PropsWithChildren } from 'react';

import cx from 'classnames';

import styles from './index.module.less';

interface Props {
  style?: CSSProperties;
  disabled?: boolean;
  className?: string;
  mode?: 'primary' | 'active' | 'default';
  onClick?: () => void;
}

const ColorfulButton = ({
  style,
  className,
  mode = 'default',
  disabled,
  children,
  onClick,
}: PropsWithChildren<Props>) => (
  <div
    className={cx(styles.button, className, {
      [styles.buttonActive]: mode === 'active',
      [styles.buttonPrimary]: mode === 'primary',
      [styles.buttonDisabled]: disabled,
    })}
    style={style}
    onClick={onClick}
  >
    {children}
  </div>
);

export default ColorfulButton;
