// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// Licensed under the 【火山方舟】原型应用软件自用许可协议
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at 
//     https://www.volcengine.com/docs/82379/1433703
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { useEffect, useRef, useState } from 'react';

// 自动滚动到底部
export const useScrollToBottom = (defaultAutoScroll?: boolean, direction: 'horizon' | 'vertical' = 'vertical') => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(Boolean(defaultAutoScroll));

  function scrollDomToBottom(to?: number) {
    const dom = scrollRef.current;
    if (dom) {
      requestAnimationFrame(() => {
        setAutoScroll(defaultAutoScroll || false);
        if (direction === 'vertical') {
          dom.scrollTo({ top: to || dom.scrollHeight, behavior: 'smooth' });
        } else {
          dom.scrollTo({ left: to || dom.scrollWidth, behavior: 'smooth' });
        }
      });
    }
  }

  // auto scroll
  useEffect(() => {
    if (autoScroll) {
      scrollDomToBottom();
    }
  });

  return {
    scrollRef,
    autoScroll,
    setAutoScroll,
    scrollDomToBottom,
  };
};
