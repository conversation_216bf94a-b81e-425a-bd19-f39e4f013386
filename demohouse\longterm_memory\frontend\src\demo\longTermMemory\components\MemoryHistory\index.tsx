// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// Licensed under the 【火山方舟】原型应用软件自用许可协议
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at 
//     https://www.volcengine.com/docs/82379/1433703
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { useEffect, useMemo, useState } from 'react';

import cx from 'classnames';
import { Popover } from '@arco-design/web-react';

import { useMemoryUpdate } from '@/demo/longTermMemory/hooks/useMemoryUpdate';
import { MemoryExtraction } from '@/demo/longTermMemory/components/MemoryExtraction';
import { UploadMemoryBtn } from '@/demo/longTermMemory/components/UploadMemoryBtn';
import { MemoryList } from '@/demo/longTermMemory/components/MemoryHistory/MemoryList';
import { listMemories } from '@/demo/longTermMemory/api';
import useFirstTimeTooltip from '@/demo/longTermMemory/hooks/useFirstTimeTooltip';
import { useChatStore } from '@/demo/longTermMemory/stores/useChatStore';

import s from './index.module.less';

const Divider = () => (
  <div className={s.intro}>
    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none">
      <mask id="mask0_813_14968" maskUnits="userSpaceOnUse" x="0" y="0" width="15" height="14">
        <rect x="0.5" width="14" height="14" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_813_14968)">
        <path
          d="M8.10287 0.599792C8.19985 0.393864 8.49254 0.393864 8.54859 0.599792L9.6745 4.73639C9.71598 4.8888 9.82476 5.00899 9.97229 5.06542L13.9743 6.59626C14.1736 6.67247 14.1446 6.96352 13.9303 7.03972L9.62392 8.57056C9.46517 8.627 9.33251 8.74718 9.26073 8.8996L7.31257 13.0362C7.21559 13.2421 6.92289 13.2421 6.86684 13.0362L5.74094 8.8996C5.69945 8.74718 5.59068 8.627 5.44315 8.57056L1.4411 7.03972C1.24188 6.96352 1.27081 6.67247 1.48518 6.59626L5.79152 5.06542C5.95027 5.00899 6.08293 4.8888 6.15471 4.73639L8.10287 0.599792Z"
          fill="#A3A3FD"
        />
      </g>
    </svg>
    <div className={s.text}>历史记忆由</div>
    <svg xmlns="http://www.w3.org/2000/svg" width="92" height="16" viewBox="0 0 92 16" fill="none">
      <path
        d="M19.483 2.00397C19.2803 1.90219 19.1921 2.09585 19.0741 2.19403C19.0335 2.22646 18.9984 2.26699 18.9642 2.30572C18.6669 2.62999 18.3201 2.84166 17.8671 2.81644C17.2041 2.77861 16.6385 2.99119 16.1377 3.50821C16.0323 2.87049 15.6783 2.49037 15.1405 2.24537C14.8586 2.11837 14.5749 1.99136 14.3776 1.71484C14.2407 1.51847 14.2029 1.29869 14.1344 1.08342C14.0903 0.95281 14.047 0.8195 13.9002 0.796981C13.7399 0.771761 13.6777 0.908673 13.6147 1.02217C13.3652 1.48965 13.2679 2.00397 13.2778 2.5246C13.2994 3.69556 13.784 4.62963 14.746 5.29258C14.8559 5.36914 14.8847 5.4457 14.8496 5.55649C14.7847 5.78528 14.7055 6.00687 14.637 6.23565C14.5938 6.38157 14.5289 6.414 14.3749 6.35005C13.8544 6.12166 13.3828 5.79503 12.986 5.38806C12.3014 4.7125 11.6826 3.96579 10.9107 3.38211C10.7323 3.247 10.5486 3.11909 10.3612 2.99749C9.57215 2.21655 10.4639 1.57522 10.6702 1.49866C10.8854 1.41939 10.7449 1.14737 10.0477 1.15007C9.35057 1.15277 8.71284 1.39147 7.89947 1.70853C7.77892 1.75529 7.65483 1.79234 7.52837 1.81932C6.76882 1.67365 5.99135 1.64601 5.22337 1.73735C3.71644 1.9085 2.51305 2.63539 1.62852 3.87661C0.565645 5.37004 0.315239 7.06524 0.62149 8.83249C0.943055 10.697 1.87532 12.2391 3.3075 13.4461C4.79192 14.6963 6.50243 15.3097 8.45343 15.1917C9.63791 15.1224 10.9575 14.9602 12.4455 13.674C12.8211 13.8649 13.2147 13.9406 13.8687 13.9982C14.3713 14.046 14.8559 13.973 15.2315 13.8938C15.8188 13.7667 15.7783 13.211 15.5657 13.1092C13.8435 12.2895 14.2218 12.6228 13.8777 12.3535C14.7532 11.296 16.0719 10.198 16.5889 6.63918C16.6295 6.35635 16.5943 6.17891 16.5889 5.95012C16.5853 5.8105 16.6159 5.75646 16.7727 5.74025C17.2059 5.69424 17.6257 5.56258 18.0076 5.35293C19.1236 4.73142 19.5749 3.70908 19.6803 2.48317C19.6965 2.29581 19.6776 2.10215 19.483 2.00397ZM9.7568 13.0326C8.08773 11.6932 7.27796 11.2519 6.94289 11.2708C6.63033 11.2897 6.68708 11.6554 6.75553 11.8932C6.82759 12.1274 6.92127 12.2895 7.05278 12.4967C7.14285 12.6327 7.20591 12.8363 6.9618 12.9885C6.42406 13.3281 5.48909 12.8741 5.44586 12.8516C4.35776 12.1986 3.44801 11.3347 2.80669 10.1539C2.18501 9.03027 1.83027 7.77867 1.76994 6.49597C1.75372 6.18071 1.8447 6.06992 2.15095 6.01227C2.55358 5.93391 2.96702 5.9231 3.37325 5.98074C5.07745 6.23475 6.52855 7.01299 7.74455 8.24431C8.43812 8.94688 8.96325 9.78457 9.5046 10.6042C10.0802 11.4735 10.699 12.3021 11.4862 12.9813C11.7655 13.2191 11.987 13.4001 12.1996 13.5335C11.5583 13.6064 10.4891 13.6226 9.7577 13.0317L9.7568 13.0326ZM10.5576 7.77862C10.5576 7.63901 10.6666 7.52822 10.8044 7.52822C10.8356 7.52822 10.8638 7.53332 10.889 7.54353C10.9364 7.56174 10.9771 7.59403 11.0056 7.63606C11.0341 7.67809 11.0491 7.72784 11.0485 7.77862C11.0489 7.81138 11.0429 7.8439 11.0307 7.87431C11.0186 7.90472 11.0005 7.93242 10.9775 7.95579C10.9546 7.97916 10.9272 7.99775 10.897 8.01048C10.8668 8.0232 10.8344 8.02981 10.8017 8.02993C10.769 8.02982 10.7367 8.02319 10.7067 8.01043C10.6767 7.99767 10.6495 7.97904 10.6267 7.95563C10.604 7.93222 10.5862 7.90451 10.5743 7.87411C10.5624 7.84371 10.5567 7.81125 10.5576 7.77862ZM13.0427 9.08109C12.8833 9.14775 12.7238 9.2054 12.5707 9.2117C12.3404 9.21962 12.1147 9.14629 11.933 9.00453C11.7141 8.81808 11.5574 8.71269 11.4916 8.38572C11.469 8.22641 11.4733 8.06443 11.5042 7.90653C11.561 7.63991 11.4979 7.46877 11.3142 7.31294C11.1638 7.18593 10.9728 7.15081 10.7638 7.15081C10.6918 7.1468 10.6219 7.12515 10.5603 7.08775C10.5344 7.07535 10.5114 7.05773 10.4927 7.03601C10.4739 7.01428 10.4599 6.98892 10.4514 6.96152C10.443 6.93411 10.4403 6.90526 10.4435 6.87676C10.4467 6.84826 10.4558 6.82074 10.4702 6.79591C10.4918 6.75088 10.5981 6.64279 10.6233 6.62477C10.9079 6.45904 11.2358 6.51308 11.5394 6.63648C11.8204 6.75358 12.033 6.96976 12.3392 7.27511C12.6518 7.64351 12.7085 7.74439 12.886 8.02092C13.0274 8.2371 13.1562 8.45868 13.2427 8.71269C13.2967 8.87212 13.2274 9.00093 13.0427 9.08109ZM52.5429 12.0409H51.5413V10.489H52.5429C53.1617 10.489 53.7886 10.3358 54.1948 9.90617C54.6011 9.47562 54.7497 8.81267 54.7497 8.15693C54.7497 7.5021 54.6011 6.84185 54.1948 6.4122C53.7895 5.98164 53.1617 5.82942 52.5429 5.82942C51.9196 5.82942 51.2963 5.98255 50.89 6.4122C50.4802 6.84275 50.3352 7.5021 50.3352 8.15693V14.5378H48.5787V4.27654H50.3352V4.92778H50.6567C50.6892 4.89175 50.7252 4.85572 50.7648 4.81969C51.2035 4.41706 51.8754 4.27654 52.5429 4.27654C53.576 4.27654 54.6173 4.53325 55.2965 5.24934C55.972 5.96543 56.2179 7.06704 56.2179 8.16054C56.2179 9.25043 55.972 10.352 55.2965 11.0672C54.6173 11.7878 53.576 12.0409 52.5429 12.0409ZM25.7927 4.54586H26.7934V6.09784H25.7927C25.173 6.09784 24.5461 6.25097 24.1398 6.68512C23.7345 7.11478 23.585 7.77502 23.585 8.42986C23.585 9.0847 23.7336 9.74494 24.1398 10.1746C24.5461 10.6051 25.173 10.7619 25.7927 10.7619C26.416 10.7619 27.0393 10.6051 27.4455 10.1746C27.8554 9.74494 28.0004 9.0847 28.0004 8.42986V2.05261H29.7568V12.3148H28.0004V11.659H27.6788C27.6466 11.6989 27.6101 11.7352 27.5699 11.7671C27.1312 12.1697 26.4601 12.3148 25.7927 12.3148C24.7595 12.3148 23.7183 12.0571 23.0382 11.3374C22.3627 10.6214 22.1177 9.52336 22.1177 8.42986C22.1177 7.33636 22.3627 6.23836 23.0382 5.51866C23.7183 4.80348 24.7586 4.54586 25.7927 4.54586ZM38.5787 8.29295V8.91626H33.8985V7.66963H37.0024C36.9304 7.21566 36.7655 6.79321 36.4755 6.48786C36.0494 6.04109 35.4027 5.88076 34.7551 5.88076C34.1119 5.88076 33.4598 6.04109 33.0382 6.48786C32.6158 6.92922 32.4627 7.61379 32.4627 8.29295C32.4627 8.97301 32.6158 9.65577 33.0382 10.1025C33.4598 10.5484 34.1119 10.706 34.7551 10.706C35.4018 10.706 36.0494 10.5484 36.4755 10.1025C36.5336 10.0377 36.5875 9.96906 36.6367 9.89717H38.3733C38.2247 10.4358 37.9797 10.9231 37.6176 11.305C36.9096 12.0481 35.8287 12.3139 34.7551 12.3139C33.6814 12.3139 32.5996 12.0481 31.8961 11.305C31.1881 10.561 30.935 9.42247 30.935 8.29295C30.935 7.16342 31.1881 6.02578 31.8961 5.28177C32.5996 4.54136 33.6814 4.27654 34.7551 4.27654C35.8287 4.27654 36.9096 4.54136 37.6176 5.28177C38.3256 6.02578 38.5787 7.16342 38.5787 8.29295ZM47.3961 8.29295V8.91626H42.7158V7.66963H45.8234C45.7513 7.21566 45.5865 6.79321 45.2928 6.48786C44.8713 6.04109 44.2236 5.88076 43.576 5.88076C42.9329 5.88076 42.2817 6.04109 41.8592 6.48786C41.4377 6.92922 41.2845 7.61379 41.2845 8.29295C41.2845 8.97301 41.4377 9.65577 41.8592 10.1025C42.2817 10.5484 42.9329 10.706 43.576 10.706C44.2245 10.706 44.8713 10.5484 45.2928 10.1025C45.3532 10.0377 45.4099 9.97013 45.4586 9.89717H47.1952C47.0421 10.4358 46.8007 10.9231 46.4386 11.305C45.7315 12.0481 44.6497 12.3139 43.576 12.3139C42.5032 12.3139 41.4214 12.0481 40.7135 11.305C40.01 10.561 39.7569 9.42247 39.7569 8.29295C39.7569 7.16342 40.01 6.02578 40.7135 5.28177C41.4214 4.54136 42.5032 4.27654 43.576 4.27654C44.6497 4.27654 45.7315 4.54136 46.4395 5.28177C47.142 6.02578 47.3961 7.16342 47.3961 8.29295ZM61.2197 12.3139C62.2934 12.3139 63.3743 12.1571 64.0778 11.7194C64.7858 11.2807 65.0389 10.6087 65.0389 9.9413C65.0389 9.27475 64.7858 8.6028 64.0778 8.16414C63.3752 7.72638 62.2934 7.56965 61.2197 7.56965H61.2558C60.7964 7.56965 60.3352 7.5093 60.0371 7.33546C59.9113 7.27066 59.8063 7.17192 59.7338 7.05044C59.6614 6.92896 59.6245 6.78961 59.6272 6.64819C59.6272 6.38698 59.7353 6.13027 60.0371 5.95732C60.3343 5.78799 60.7973 5.72764 61.2558 5.72764C61.7142 5.72764 62.1763 5.78799 62.4781 5.95732C62.6035 6.02302 62.708 6.12259 62.7796 6.24472C62.8513 6.36685 62.8872 6.50664 62.8834 6.64819H64.6732C64.6732 5.98164 64.4444 5.30969 63.8084 4.87103C63.1689 4.43327 62.1925 4.27654 61.2197 4.27654C60.2469 4.27654 59.2696 4.43327 58.6301 4.87103C57.9906 5.30969 57.7618 5.98074 57.7618 6.64819C57.7618 7.31564 57.9906 7.98759 58.6301 8.42535C59.2696 8.86401 60.246 9.02074 61.2197 9.02074C61.7259 9.02074 62.2772 9.08109 62.6105 9.25043C62.9437 9.41887 63.0608 9.68009 63.0608 9.9413C63.0608 10.1989 62.9437 10.4601 62.6105 10.6295C62.2772 10.7979 61.7665 10.8619 61.2594 10.8619C60.7531 10.8619 60.2415 10.7988 59.9127 10.6295C59.5795 10.4601 59.4588 10.1989 59.4588 9.9413H57.3997C57.3997 10.6087 57.6528 11.2807 58.3563 11.7185C59.0643 12.1571 60.1415 12.3139 61.2197 12.3139ZM73.8571 8.29295V8.91626H69.176V7.66963H72.2844C72.2124 7.21566 72.0475 6.79321 71.753 6.48786C71.3314 6.04109 70.6838 5.88076 70.0371 5.88076C69.3939 5.88076 68.7418 6.04109 68.3203 6.48786C67.8978 6.92922 67.7447 7.61379 67.7447 8.29295C67.7447 8.97301 67.8978 9.65577 68.3203 10.1025C68.7418 10.5484 69.3939 10.706 70.0371 10.706C70.6838 10.706 71.3314 10.5484 71.7539 10.1025C71.8144 10.0387 71.8695 9.97005 71.9187 9.89717H73.6553C73.5022 10.4358 73.2617 10.9231 72.8996 11.305C72.1916 12.0481 71.1108 12.3139 70.0371 12.3139C68.9634 12.3139 67.8816 12.0481 67.1745 11.305C66.4701 10.561 66.217 9.42247 66.217 8.29295C66.217 7.16342 66.4701 6.02578 67.1745 5.28177C67.8816 4.54136 68.9634 4.27654 70.0371 4.27654C71.1108 4.27654 72.1916 4.54136 72.8996 5.28177C73.6031 6.02578 73.8571 7.16342 73.8571 8.29295ZM82.6781 8.29295V8.91626H77.9978V7.66963H81.1054C81.0297 7.21566 80.8685 6.79321 80.5757 6.48786C80.1524 6.04109 79.5021 5.88076 78.858 5.88076C78.2149 5.88076 77.5637 6.04109 77.1412 6.48786C76.7152 6.92922 76.5665 7.61379 76.5665 8.29295C76.5665 8.97301 76.7152 9.65577 77.1412 10.1025C77.5637 10.5484 78.2149 10.706 78.858 10.706C79.5021 10.706 80.1533 10.5484 80.5757 10.1025C80.6352 10.0377 80.6874 9.97013 80.7397 9.89717H82.4772C82.3241 10.4358 82.0827 10.9231 81.717 11.305C81.0135 12.0481 79.9317 12.3139 78.858 12.3139C77.7852 12.3139 76.6989 12.0481 75.9955 11.305C75.292 10.561 75.0389 9.42247 75.0389 8.29295C75.0389 7.16342 75.292 6.02578 75.9955 5.28177C76.6989 4.54136 77.7852 4.27654 78.858 4.27654C79.9317 4.27654 81.0135 4.54136 81.717 5.28177C82.425 6.02578 82.6781 7.16342 82.6781 8.29295ZM83.8607 1.69502H85.6181V12.3166H83.8607V1.69502ZM88.6167 8.05155L91.4999 12.3139H89.3201L86.4378 8.05155L89.3201 4.63414H91.4999L88.6167 8.05155Z"
        fill="#5252FF"
      />
    </svg>
    <div className={s.text}>进行抽取</div>
    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none">
      <mask id="mask0_813_14976" maskUnits="userSpaceOnUse" x="0" y="0" width="15" height="14">
        <rect x="0.5" width="14" height="14" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_813_14976)">
        <path
          d="M8.10287 0.599792C8.19985 0.393864 8.49254 0.393864 8.54859 0.599792L9.6745 4.73639C9.71598 4.8888 9.82476 5.00899 9.97229 5.06542L13.9743 6.59626C14.1736 6.67247 14.1446 6.96352 13.9303 7.03972L9.62392 8.57056C9.46517 8.627 9.33251 8.74718 9.26073 8.8996L7.31257 13.0362C7.21559 13.2421 6.92289 13.2421 6.86684 13.0362L5.74094 8.8996C5.69945 8.74718 5.59068 8.627 5.44315 8.57056L1.4411 7.03972C1.24188 6.96352 1.27081 6.67247 1.48518 6.59626L5.79152 5.06542C5.95027 5.00899 6.08293 4.8888 6.15471 4.73639L8.10287 0.599792Z"
          fill="#A3A3FD"
        />
      </g>
    </svg>
  </div>
);
export const MemoryHistory = () => {
  const { setMemoryList, presetMemoryList, memoryList, isReasoning, reasoningContent, reasonStartTime, reasonEndTime } =
    useMemoryUpdate();
  const { chatList } = useChatStore();

  // 定期轮询更新 memory list
  useEffect(() => {
    const intervalId = setInterval(() => {
      listMemories(setMemoryList);
    }, 10000);

    return () => {
      clearInterval(intervalId);
    };
  }, [setMemoryList]);

  const [firstTimeUse, dissmiss] = useFirstTimeTooltip('memory_bot_upload');
  const [showTooltip, setShowTooltip] = useState(false);
  useEffect(() => {
    const filtered = chatList.filter(item => item.role !== 'divider');
    const last = filtered[filtered.length - 1];
    if (firstTimeUse && filtered.length >= 6 && last.finish) {
      setShowTooltip(true);
      setTimeout(() => {
        setShowTooltip(false);
        dissmiss();
      }, 3000);
    }
  }, [chatList, firstTimeUse]);

  const memoryListDisplay = useMemo(
    () => (memoryList.length === 0 ? presetMemoryList : memoryList),
    [presetMemoryList, memoryList],
  );

  return (
    <div className={s.container}>
      <div className={s.title}>
        <div>历史记忆</div>
        {Boolean(memoryListDisplay.length) && <div className={s.count}>共{memoryListDisplay.length}条</div>}
      </div>
      <div className={cx('flex-1 h-fit overflow-x-hidden overflow-y-auto -mx-6 px-6', s.noScrollBar)}>
        <MemoryList memoryList={memoryListDisplay} />
        <Divider />
      </div>
      <div className={s.upload}>
        <MemoryExtraction
          isReasoning={isReasoning}
          reasoningContent={reasoningContent}
          startTime={reasonStartTime}
          endTime={reasonEndTime}
        />
        <div className={'w-full flex flex-col items-center mb-[11px]'}>
          <Popover content={'更新记忆并开启新一轮个性化问答'} popupVisible={showTooltip}>
            <UploadMemoryBtn />
          </Popover>
        </div>
        <div className={s.typo}>点击【记忆更新】按钮将当前对话上传抽取成记忆，建议不少于 3 轮</div>
      </div>
    </div>
  );
};
