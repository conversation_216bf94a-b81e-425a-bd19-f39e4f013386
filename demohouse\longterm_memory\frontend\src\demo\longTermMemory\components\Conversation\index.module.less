.welc {
  display: flex;
  width: 100%;
  height: 296px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .avatar {
    width: 72px;
    height: 72px;
    border-radius: 44px;
    margin-bottom: 16px;
  }
  .title {
    margin-bottom: 4px;
    color: var(--text-color-text-1, #0C0D0E);
    text-align: center;

    /* Title/title-1 */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 150% */
    letter-spacing: 0.048px;
  }
  .desc {
    margin-bottom: 34px;
    color: var(--text-color-text-3, #737A87);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 169.231% */
    letter-spacing: 0.039px;
  }
  .list {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    .q {
      cursor: pointer;

      display: flex;
      height: 36px;
      padding: 7px 25px;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      border: 1px solid var(--light-line-color-border-1, #F2F3F5);
      opacity: 0.8;
      background: var(--fill-color-fill-5, #FFF);
      color: var(--text-color-text-1, #0C0D0E);

      /* Body/body-2 regular */
      font-family: "PingFang SC";
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 169.231% */
      letter-spacing: 0.039px;
    }
  }
}


.msg-list {
  background:#F3F7FF;
  max-width: 800px;
  min-width: 550px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  flex:1;
  display: flex;
  flex-direction: column;
}

.chat {
  background:#F3F7FF;
  max-height: 100vh;
  max-width: 980px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  flex:1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}


.no-scroll-bar{
  &::-webkit-scrollbar {
    display: none;
  }
}

