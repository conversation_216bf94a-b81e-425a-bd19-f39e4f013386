.btn {
  display: flex;
  width: 265px;
  padding: 10px 42px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  align-self: stretch;
  border-radius: var(--border-radius-circle, 99px);
  border: 2px solid var(--ai-primary-color-primary-6, #3B91FF);
  background: var(--background-color-bg-1, #FFF);
  box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.08);
  color: var(--text-color-text-1, var(--text-color-text-1, #0C0D0E));
  text-align: center;

  /* Body/body-2 medium */
  font-family: "PingFang SC";
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px; /* 169.231% */
  letter-spacing: 0.039px;
}
.disabled {
  border-radius: var(--border-radius-circle, 99px);
  border: 2px solid var(--ai-primary-color-border-primary-4, #8AC5FF);
  background: var(--background-color-bg-1, #FFF);

  color: var(--text-color-text-3, var(--text-color-text-3, #737A87));
  text-align: center;

  /* Body/body-2 medium */
  font-family: "PingFang SC";
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px; /* 169.231% */
  letter-spacing: 0.039px;
}
