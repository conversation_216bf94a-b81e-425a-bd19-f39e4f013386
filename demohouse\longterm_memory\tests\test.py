# # 云服务
# from mem0 import MemoryClient
# client = MemoryClient(api_key="m0-j5Zno0q53AJFhNGHFU2A2VN3xtquv2QjAdoGiHNh")

# messages = [
#     {"role": "user", "content": "Hi, I'm <PERSON>. I'm a vegetarian and I'm allergic to nuts."},
#     {"role": "assistant", "content": "Hello <PERSON>! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions."}
# ]
# # client.add(messages, user_id="alex")

# query = "What can I cook for dinner tonight?"
# result = client.search(query, user_id="alex")
# print(result)

import os
from openai import OpenAI
from mem0 import Memory

    # "embedder": {
    #     "provider": "azure_openai",
    #     "config": {
    #         "model": "text-embedding-3-large",
    #         "azure_kwargs": {
    #               "api_version": "",
    #               "azure_deployment": "",
    #               "azure_endpoint": "",
    #               "api_key": "",
    #               "dimensions": 3072
    #           }
    #     }
    # },

    # "embedder": {
    #     "provider": "huggingface",
    #     "config": {
    #         "model": "D:\\models\\nlp_gte_sentence-embedding_chinese-small"
    #     }
    # },

os.environ["EMBEDDING_AZURE_OPENAI_API_KEY"] = "8cef36d7afe85700"
os.environ["EMBEDDING_AZURE_DEPLOYMENT"] = "SVW-EMBEDDING-LARGE"
os.environ["EMBEDDING_AZURE_ENDPOINT"] = "https://openai-svw2.openai.azure.com/"
os.environ["EMBEDDING_AZURE_API_VERSION"] = "2024-10-21"

os.environ["LLM_AZURE_OPENAI_API_KEY"] = "e738060e605f6ff6c5"
os.environ["LLM_AZURE_DEPLOYMENT"] = "SVW-GPT4o-IS"
os.environ["LLM_AZURE_ENDPOINT"] = "https://openai-svw4.openai.azure.com/"
os.environ["LLM_AZURE_API_VERSION"] = "2024-05-01-preview"

config = {
    "vector_store": {
        "provider": "faiss",
        "config": {
            "collection_name": "test",
            "path": "D:\\codes\\gpt\\ai-app-lab\\demohouse\\longterm_memory\\tests\\tmp\\faiss_memories",
            "distance_strategy": "euclidean"
        }
    },
    "embedder": {
        "provider": "azure_openai",
        "config": {
            "model": "text-embedding-3-large",
            "azure_kwargs": {
                  "api_version": "",
                  "azure_deployment": "",
                  "azure_endpoint": "",
                  "api_key": ""
              }
        }
    },
    "llm": {
        "provider": "azure_openai",
        "config": {
            "model": "SVW-GPT4o-IS",
            "temperature": 0.1,
            "max_tokens": 2000,
            "azure_kwargs": {
                  "azure_deployment": "",
                  "api_version": "",
                  "azure_endpoint": "",
                  "api_key": ""
              }
        }
    }
}

# openai_client = OpenAI(api_key = "f9dc9e22-a0ef-4b8d-939d-ce9ed0a8bbe4", base_url = "https://ark.cn-beijing.volces.com/api/v3")
memory = Memory.from_config(config)

# def chat_with_memories(message: str, user_id: str = "default_user") -> str:
#     # Retrieve relevant memories
#     relevant_memories = memory.search(query=message, user_id=user_id, limit=3)
#     memories_str = "\n".join(f"- {entry['memory']}" for entry in relevant_memories["results"])

#     # Generate Assistant response
#     system_prompt = f"You are a helpful AI. Answer the question based on query and memories.\nUser Memories:\n{memories_str}"
#     messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": message}]
#     response = openai_client.chat.completions.create(model="ep-20250331151844-7qvsb", messages=messages)
#     assistant_response = response.choices[0].message.content

#     # Create new memories from the conversation
#     messages.append({"role": "assistant", "content": assistant_response})
#     memory.add(messages, user_id=user_id)

#     return assistant_response

# def main():
#     print("Chat with AI (type 'exit' to quit)")
#     while True:
#         user_input = input("You: ").strip()
#         if user_input.lower() == 'exit':
#             print("Goodbye!")
#             break
#         print(f"AI: {chat_with_memories(user_input)}")

# if __name__ == "__main__":
#     main()


messages = [
    {"role": "user", "content": "I'm planning to watch a movie tonight. Any recommendations?"},
    {"role": "assistant", "content": "How about a thriller movies? They can be quite engaging."},
    {"role": "user", "content": "I'm not a big fan of thriller movies but I love sci-fi movies."},
    {"role": "assistant", "content": "Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future."}
]

# Store inferred memories (default behavior)
result = memory.add(messages, user_id="alice", metadata={"category": "movie_recommendations"})

# Store raw messages without inference
# result = m.add(messages, user_id="alice", metadata={"category": "movie_recommendations"}, infer=False)